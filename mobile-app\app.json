{"expo": {"name": "QaaqConnect Mariana", "slug": "qaaqconnect-mariana", "version": "2.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#dc2626"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.qaaq.mariana", "infoPlist": {"NSLocationWhenInUseUsageDescription": "QaaqConnect uses location to help you find nearby sailors and maritime professionals.", "NSLocationAlwaysAndWhenInUseUsageDescription": "QaaqConnect uses location to help you find nearby sailors and maritime professionals.", "NSCameraUsageDescription": "QaaqConnect uses camera to take profile pictures and scan QR codes."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#dc2626"}, "package": "com.qaaq.mariana", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-camera"]}}